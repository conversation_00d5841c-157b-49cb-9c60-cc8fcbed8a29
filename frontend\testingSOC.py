import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from database import get_connection, insert_row
import datetime

if __name__ == "__main__":
    st.set_page_config(page_icon="📊", layout="wide", initial_sidebar_state="expanded")

def load_INCIDENTS():
    with st.spinner("Connexion à la base de données..."):
        try:
            conn = get_connection()
            query = 'SELECT * FROM "INCIDENTS"'
            df = pd.read_sql(query, conn)
            conn.close()
            return df
        except Exception as e:
            st.error(f"Erreur base de données : {e}")
            return None

def apply_priority_colors(sub_df, priorite_col='Criticité'):
    def priority_color(row):
        if row[priorite_col] == 'P1':
            return ['background-color: #FF0000; color: white'] * len(row)
        elif row[priorite_col] == 'P2':
            return ['background-color: #ED7D31; color: black'] * len(row)
        elif row[priorite_col] == 'P3':
            return ['background-color: #FFC000; color: black'] * len(row)
        else:
            return [''] * len(row)
    return sub_df.style.apply(priority_color, axis=1).hide(axis='index').set_table_styles(
        [{'selector': 'th', 'props': [('text-align', 'center')]}])

def make_clickable(url):
    if pd.notnull(url) and str(url).strip():
        return f'<a href="{url}" target="_blank" style="color: inherit; text-decoration: none;">{url}</a>'
    else:
        return ''

def main():
    st.title("Suivi des Alertes SOC")

    df = load_INCIDENTS()
    if df is not None:
        

        month_order = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                       'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre']

        annee_options = sorted(df['Année'].dropna().unique())
        criticite_options = df['criticité'].dropna()
        criticite_options = criticite_options[criticite_options.astype(str).str.strip().isin(['P1', 'P2', 'P3'])].unique()

 
        status_options = df['status'].dropna().unique() if 'status' in df.columns else []

        col1, col2 = st.columns(2)
        default_annee = ['2025'] if '2025' in annee_options else []
        selected_annee = col1.multiselect("Année", annee_options, default=default_annee)

        if selected_annee:
            Mois_options_raw = df[df['Année'].isin(selected_annee)]['Mois'].dropna().unique()
        else:
            Mois_options_raw = df['Mois'].dropna().unique()

        Mois_options = [m for m in month_order if m in Mois_options_raw]

        selected_Mois = col2.multiselect("Mois", Mois_options, placeholder="Choisissez une option")

        col3, col5 = st.columns([1, 1])
        selected_criticite = col3.multiselect("Criticité", sorted(criticite_options), placeholder="Choisissez une option")

        selected_status = col5.multiselect("status", sorted(status_options), placeholder="Choisissez une option")

        filtered_df = df.copy()
        filtered_df = filtered_df[filtered_df['criticité'].notna() & (filtered_df['criticité'].astype(str).str.strip() != '')]


        if selected_annee:
            filtered_df = filtered_df[filtered_df['Année'].isin(selected_annee)]
        if selected_Mois:
            filtered_df = filtered_df[filtered_df['Mois'].isin(selected_Mois)]
        if selected_criticite:
            filtered_df = filtered_df[filtered_df['criticité'].isin(selected_criticite)]
      
      
        if selected_status:
            filtered_df = filtered_df[filtered_df['status'].isin(selected_status)]

        display_columns = ['incident id', 'description', 'criticité', 'ticket jira']
        # Reapply filter to remove empty Criticité values after search
        filtered_df = filtered_df[filtered_df['criticité'].notna() & (filtered_df['criticité'].astype(str).str.strip() != '')]
        filtered_table_data = filtered_df[display_columns] if not filtered_df.empty else pd.DataFrame(columns=display_columns)
        filtered_table_data = filtered_table_data.rename(columns={
            'incident id': 'incident ID',
            'description': 'Description incident',
            'criticité': 'Criticité',
            'ticket jira': 'Ticket Jira'
        })
        if 'Ticket Jira' in filtered_table_data.columns:
            filtered_table_data['Ticket Jira'] = filtered_table_data['Ticket Jira'].apply(make_clickable)
        filtered_table_data['Criticité'] = pd.Categorical(filtered_table_data['Criticité'], categories=['P1', 'P2', 'P3'], ordered=True)
        filtered_table_data = filtered_table_data.sort_values(by='Criticité')

        P1_count = filtered_df.loc[filtered_df['criticité'] == 'P1'].shape[0]
        P2_count = filtered_df.loc[filtered_df['criticité'] == 'P2'].shape[0]
        P3_count = filtered_df.loc[filtered_df['criticité'] == 'P3'].shape[0]
        total_alerts = P1_count + P2_count + P3_count

        st.markdown(f"### Le nombre d'alertes SOC: {total_alerts}")
        st.markdown("_ _ _")
        filtered_df = filtered_df[filtered_df['criticité'].isin(['P1', 'P2', 'P3'])]

        crit_count = filtered_df['criticité'].value_counts().reset_index()
        crit_count.columns = ['criticité', 'COUNT']

        custom_colors = {'P1': '#FF0000', 'P2': '#ED7D31', 'P3': '#FFC000'}
        bar_colors = [custom_colors.get(c, '#808080') for c in crit_count['criticité']]

        col1, col2 = st.columns(2)

        with col1:
            fig = go.Figure()
            fig.add_trace(go.Bar(
                x=crit_count['criticité'],
                y=crit_count['COUNT'],
                marker_color=bar_colors,
                text=crit_count['COUNT'],
                textposition='inside',
                
            ))
            fig.update_layout(
                xaxis_title='Criticité',
                yaxis_title='Nombre de failles',
                plot_bgcolor='rgba(0,0,0,0)',
                title="Nombre de failles par criticité",
                paper_bgcolor='rgba(0,0,0,0)',
                font=dict(color='black')
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            pie_data = crit_count.copy()
            pie_data.columns = ['criticité', 'count']

            fig_pie = px.pie(pie_data,
                             values='count',
                             names='criticité',
                             color='criticité',
                             color_discrete_map=custom_colors)
            st.plotly_chart(fig_pie, use_container_width=True)

        search_query = st.text_input("🔍 Rechercher dans le tableau: ").strip().lower()
        if search_query:
            filtered_df = filtered_df[filtered_df.apply(lambda row: row.astype(str).str.lower().str.contains(search_query).any(), axis=1)]
            filtered_df = filtered_df[filtered_df['criticité'].notna() & (filtered_df['criticité'].astype(str).str.strip() != '')]

        filtered_table_data = filtered_df[display_columns] if not filtered_df.empty else pd.DataFrame(columns=display_columns)
        filtered_table_data = filtered_table_data.rename(columns={
            'incident id': 'incident ID',
            'description': 'Description incident',
            'criticité': 'Criticité',
            'ticket jira': 'Ticket Jira'
        })
        if 'Ticket Jira' in filtered_table_data.columns:
            filtered_table_data['Ticket Jira'] = filtered_table_data['Ticket Jira'].apply(make_clickable)
        filtered_table_data['Criticité'] = pd.Categorical(filtered_table_data['Criticité'], categories=['P1', 'P2', 'P3'], ordered=True)
        filtered_table_data = filtered_table_data.sort_values(by='Criticité')

        if not filtered_table_data.empty:
            _, col_toggle = st.columns([8, 1])
            show_table = col_toggle.toggle("Afficher", value=True)

            if show_table:
                styled_table = apply_priority_colors(filtered_table_data)
                table_html = styled_table.to_html(escape=False)
                centered_table_html = f"""
                <div style="margin-top: 20px;">
                    {table_html}
                """
                st.write(centered_table_html, unsafe_allow_html=True)
        else:
            st.info("Aucune ligne trouvée pour les filtres et la recherche actuels.")
    else:
        st.warning("Aucune donnée disponible.")

    # === ADD THIS PART FOR USER CHECK ===

    user_info = st.session_state.get('keycloak_user_info', {})
    username = user_info.get('preferred_username', '')
    email = user_info.get('email', '')

    if username == 'nour.bachraoui' or email == '<EMAIL>' or email == '<EMAIL>':
        st.markdown("---")
        st.subheader("Ajouter une nouvelle ligne")

        with st.form("add_row_form"):
            # Incident ID input with auto "INC-" prefix and numeric validation
            incident_id_input = st.text_input(
                "ID Incident (format: INC-XXXX)",
                placeholder="XXXX",
                max_chars=4
            )

            date_incident = st.date_input("Date du incident", datetime.date.today())
            description = st.text_input("Description incident")

            criticite = st.selectbox("Criticité", ["P1", "P2", "P3"])
            type_incident = st.text_input("Type d'incident")
            ticket_jira = st.text_input("Ticket JIRA")
            responsable = st.text_input("responsable")
            status = st.selectbox("status", ["clôs", "en cours"])
            commentaire = st.text_input("Commentaire")

            submitted = st.form_submit_button("Ajouter")

            if submitted:
                # French month names mapping
                months_fr = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
                           "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]

                annee = str(date_incident.year)
                Mois = months_fr[date_incident.month - 1]

                # Validate incident ID format
                validation_error = False
                incident_id = None

                if not incident_id_input.strip():
                    st.error("Veuillez entrer un numéro d'incident après 'INC-'")
                    validation_error = True
                elif not incident_id_input.strip().isdigit() or len(incident_id_input.strip()) != 4:
                    st.error("L'ID incident doit être composé exactement de 4 chiffres (ex: 2355)")
                    validation_error = True
                else:
                    incident_id = "INC-" + incident_id_input.strip()

                # Only proceed if validation passes
                if not validation_error:
                    row_data = {
                        "incident id": incident_id,
                        "date d'incident": date_incident.strftime('%d-%b-%Y').upper(),
                        "description": description,
                        "criticité": criticite,
                        "type": type_incident,
                        "ticket jira": ticket_jira,
                        "responsable": responsable,
                        "status": status,
                        "commentaire": commentaire,
                        "Année": annee,
                        "Mois": Mois
                    }

                    try:
                        conn = get_connection()
                        success, error_msg = insert_row(conn, "INCIDENTS", row_data)
                        conn.close()

                        if success:
                            st.success("✅ Nouvelle ligne ajoutée avec succès !")
                            st.rerun()  # Refresh the page to show the new data
                        else:
                            st.error(f"❌ Erreur lors de l'ajout : {error_msg}")
                    except Exception as e:
                        st.error(f"❌ Exception inattendue : {e}")

if __name__ == "__main__":
    main()
