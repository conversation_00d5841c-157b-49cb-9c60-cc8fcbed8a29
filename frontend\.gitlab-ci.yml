stages:
  - build
  - deploy

variables:
  DOCKER_IMAGE_BACKEND: "registry.gitlab.com/your_repo/backend"
  *********************: "registry.gitlab.com/your_repo/frontend"

build-backend:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t $DOCKER_IMAGE_BACKEND:latest -f backend/Dockerfile .
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $DOCKER_IMAGE_BACKEND:latest

build-frontend:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t $*********************:latest -f frontend/Dockerfile .
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $*********************:latest

deploy:
  stage: deploy
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker pull $DOCKER_IMAGE_BACKEND:latest
    - docker pull $*********************:latest
    - docker run -d -p 8000:8000 --name backend $DOCKER_IMAGE_BACKEND:latest
    - docker run -d -p 8501:8501 --name frontend $*********************:latest
  environment: production
