import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
from database import get_connection, insert_row
import datetime

if __name__ == "__main__":
    st.set_page_config(page_icon="📊", layout="wide", initial_sidebar_state="expanded")

def load_suivi_bulletins():
    with st.spinner("Connexion à la base de données..."):
        try:
            conn = get_connection()
            query = 'SELECT * FROM "SUIVI_BULLETINS"'
            df = pd.read_sql(query, conn)
            conn.close()
            return df
        except Exception as e:
            st.error(f"Erreur base de données : {e}")
            return None

def apply_priority_colors(sub_df, priorite_col='Criticité'):
    def priority_color(row):
        if row[priorite_col] == 'P1':
            return ['background-color: #FF0000; color: white'] * len(row)
        elif row[priorite_col] == 'P2':
            return ['background-color: #ED7D31; color: black'] * len(row)
        elif row[priorite_col] == 'P3':
            return ['background-color: #FFC000; color: black'] * len(row)
        else:
            return [''] * len(row)
    return sub_df.style.apply(priority_color, axis=1).hide(axis='index').set_table_styles(
        [{'selector': 'th', 'props': [('text-align', 'center')]}])

def make_clickable(url):
    if pd.notnull(url) and str(url).strip():
        return f'<a href="{url}" target="_blank" style="color: inherit; text-decoration: none;">{url}</a>'
    else:
        return ''

def main():
    st.title("Suivi des Bulletins CERT")

    df = load_suivi_bulletins()
    if df is not None:
        df['PERIMETRE'] = df['PERIMETRE'].replace([np.nan, 'nan'], 'En cours de vérification')

        month_order = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                       'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre']

        annee_options = sorted(df['ANNEE'].dropna().unique())
        criticite_options = df['CRITICITE'].dropna()
        criticite_options = criticite_options[criticite_options.astype(str).str.strip().isin(['P1', 'P2', 'P3'])].unique()

        perimetre_options = df['PERIMETRE'].unique()
        status_options = df['STATUS'].dropna().unique() if 'STATUS' in df.columns else []

        col1, col2 = st.columns(2)
        default_annee = ['2025'] if '2025' in annee_options else []
        selected_annee = col1.multiselect("Année", annee_options, default=default_annee)

        if selected_annee:
            mois_options_raw = df[df['ANNEE'].isin(selected_annee)]['MOIS'].dropna().unique()
        else:
            mois_options_raw = df['MOIS'].dropna().unique()

        mois_options = [m for m in month_order if m in mois_options_raw]

        selected_mois = col2.multiselect("Mois", mois_options, placeholder="Choisissez une option")

        col3, col4, col5 = st.columns(3)
        selected_criticite = col3.multiselect("Criticité", sorted(criticite_options), placeholder="Choisissez une option")
        selected_perimetre = col4.multiselect("Périmètre", sorted(perimetre_options), placeholder="Choisissez une option")
        selected_status = col5.multiselect("Status", sorted(status_options), placeholder="Choisissez une option")

        filtered_df = df.copy()
        filtered_df = filtered_df[filtered_df['CRITICITE'].notna() & (filtered_df['CRITICITE'].astype(str).str.strip() != '')]


        if selected_annee:
            filtered_df = filtered_df[filtered_df['ANNEE'].isin(selected_annee)]
        if selected_mois:
            filtered_df = filtered_df[filtered_df['MOIS'].isin(selected_mois)]
        if selected_criticite:
            filtered_df = filtered_df[filtered_df['CRITICITE'].isin(selected_criticite)]
        if selected_perimetre:
            filtered_df = filtered_df[filtered_df['PERIMETRE'].isin(selected_perimetre)]
        if selected_status:
            filtered_df = filtered_df[filtered_df['STATUS'].isin(selected_status)]

        display_columns = ['CERT_ID', 'DESCRIPTION_BULLETIN', 'CRITICITE', 'TICKET_JIRA']
        # Reapply filter to remove empty Criticité values after search
        filtered_df = filtered_df[filtered_df['CRITICITE'].notna() & (filtered_df['CRITICITE'].astype(str).str.strip() != '')]
        filtered_table_data = filtered_df[display_columns] if not filtered_df.empty else pd.DataFrame(columns=display_columns)
        filtered_table_data = filtered_table_data.rename(columns={
            'CERT_ID': 'CERT ID',
            'DESCRIPTION_BULLETIN': 'Description bulletin',
            'CRITICITE': 'Criticité',
            'TICKET_JIRA': 'Ticket Jira'
        })
        if 'Ticket Jira' in filtered_table_data.columns:
            filtered_table_data['Ticket Jira'] = filtered_table_data['Ticket Jira'].apply(make_clickable)
        filtered_table_data['Criticité'] = pd.Categorical(filtered_table_data['Criticité'], categories=['P1', 'P2', 'P3'], ordered=True)
        filtered_table_data = filtered_table_data.sort_values(by='Criticité')

        P1_count = filtered_df.loc[filtered_df['CRITICITE'] == 'P1'].shape[0]
        P2_count = filtered_df.loc[filtered_df['CRITICITE'] == 'P2'].shape[0]
        P3_count = filtered_df.loc[filtered_df['CRITICITE'] == 'P3'].shape[0]

        first_column, second_column, third_column = st.columns(3)
        with first_column:
            st.markdown("### P1:")
            st.subheader(f'{P1_count}')
        with second_column:
            st.markdown("### P2:")
            st.subheader(f'{P2_count}')
        with third_column:
            st.markdown("### P3:")
            st.subheader(f'{P3_count}')
        st.markdown("_ _ _")
        filtered_df = filtered_df[filtered_df['CRITICITE'].isin(['P1', 'P2', 'P3'])]

        crit_count = filtered_df['CRITICITE'].value_counts().reset_index()
        crit_count.columns = ['CRITICITE', 'COUNT']

        custom_colors = {'P1': '#FF0000', 'P2': '#ED7D31', 'P3': '#FFC000'}
        bar_colors = [custom_colors.get(c, '#808080') for c in crit_count['CRITICITE']]

        col1, col2 = st.columns(2)

        with col1:
            fig = go.Figure()
            fig.add_trace(go.Bar(
                x=crit_count['CRITICITE'],
                y=crit_count['COUNT'],
                marker_color=bar_colors,
                text=crit_count['COUNT'],
                textposition='inside',
                
            ))
            fig.update_layout(
                xaxis_title='Criticité',
                yaxis_title='Nombre de failles',
                plot_bgcolor='rgba(0,0,0,0)',
                title="Nombre de failles par criticité",
                paper_bgcolor='rgba(0,0,0,0)',
                font=dict(color='black')
            )
            st.plotly_chart(fig, use_container_width=True)

        with col2:
            pie_data = crit_count.copy()
            pie_data.columns = ['criticité', 'count']

            fig_pie = px.pie(pie_data,
                             values='count',
                             names='criticité',
                             color='criticité',
                             color_discrete_map=custom_colors)
            st.plotly_chart(fig_pie, use_container_width=True)

        search_query = st.text_input("🔍 Rechercher dans le tableau: ").strip().lower()
        if search_query:
            filtered_df = filtered_df[filtered_df.apply(lambda row: row.astype(str).str.lower().str.contains(search_query).any(), axis=1)]
            filtered_df = filtered_df[filtered_df['CRITICITE'].notna() & (filtered_df['CRITICITE'].astype(str).str.strip() != '')]

        filtered_table_data = filtered_df[display_columns] if not filtered_df.empty else pd.DataFrame(columns=display_columns)
        filtered_table_data = filtered_table_data.rename(columns={
            'CERT_ID': 'CERT ID',
            'DESCRIPTION_BULLETIN': 'Description bulletin',
            'CRITICITE': 'Criticité',
            'TICKET_JIRA': 'Ticket Jira'
        })
        if 'Ticket Jira' in filtered_table_data.columns:
            filtered_table_data['Ticket Jira'] = filtered_table_data['Ticket Jira'].apply(make_clickable)
        filtered_table_data['Criticité'] = pd.Categorical(filtered_table_data['Criticité'], categories=['P1', 'P2', 'P3'], ordered=True)
        filtered_table_data = filtered_table_data.sort_values(by='Criticité')

        if not filtered_table_data.empty:
            _, col_toggle = st.columns([8, 1])
            show_table = col_toggle.toggle("Afficher", value=True)

            if show_table:
                styled_table = apply_priority_colors(filtered_table_data)
                table_html = styled_table.to_html(escape=False)
                centered_table_html = f"""
                <div style="margin-top: 20px;">
                    {table_html}
                """
                st.write(centered_table_html, unsafe_allow_html=True)
        else:
            st.info("Aucune ligne trouvée pour les filtres et la recherche actuels.")
    else:
        st.warning("Aucune donnée disponible.")

    # === ADD THIS PART FOR USER CHECK ===
    import calendar

    user_info = st.session_state.get('keycloak_user_info', {})
    username = user_info.get('preferred_username', '')
    email = user_info.get('email', '')

    if username == 'nour.bachraoui' or email == '<EMAIL>' or email == '<EMAIL>':
        st.markdown("---")
        st.subheader("Ajouter une nouvelle ligne")

        with st.form("add_row_form"):
            cert_id_date = st.date_input("Date pour CERT ID", datetime.date.today())
            date_bulletin = st.date_input("Date du Bulletin", datetime.date.today())
            description = st.text_input("Description Bulletin")
            score = st.text_input("score")
            criticite = st.selectbox("Criticité", ["P1", "P2", "P3"])
            ticket_jira = st.text_input("Ticket JIRA")
            perimetre = st.selectbox("Périmètre", ["Inclus", "En cours de vérification", "Non inclus"])
            status = st.selectbox("Status", ["clôs", "en cours"])
            commentaire = st.text_input("Commentaire")
            responsable = st.text_input("Responsable")
            exposition_sur_internet = st.selectbox("Exposition sur Internet", ["oui", "non"])
            submitted = st.form_submit_button("Ajouter")
            
            if submitted:
                annee = str(date_bulletin.year)
                mois = calendar.month_name[date_bulletin.month]
                cert_id = f"ID: {cert_id_date.strftime('%Y-%m-%d')}"

                row_data = {
                    "CERT_ID": cert_id,
                    "DATE_DU_BULLETIN": date_bulletin.strftime('%Y-%m-%d 00:00:00'),
                    "DESCRIPTION_BULLETIN": description,
                    "CRITICITE": criticite,
                    "TICKET_JIRA": ticket_jira,
                    "ANNEE": annee,
                    "MOIS": mois,
                    "PERIMETRE": perimetre,
                    "STATUS": status,
                    "SCORE": score,
                    "COMMENTAIRE": commentaire,
                    "RESPONSABLE": responsable,
                    "EXPOSITION_SUR_INTERNET": exposition_sur_internet,
                }

                try:
                    conn = get_connection()
                    success, error_msg = insert_row(conn, "SUIVI_BULLETINS", row_data)
                    conn.close()

                    if success:
                        st.success("✅ Nouvelle ligne ajoutée avec succès !")
                    else:
                        st.error(f"❌ Erreur lors de l'ajout : {error_msg}")
                except Exception as e:
                    st.error(f"❌ Exception inattendue : {e}")

if __name__ == "__main__":
    main()
