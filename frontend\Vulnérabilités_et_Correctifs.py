import streamlit as st
import pandas as pd
import os

def app():
    # Function to read Excel files and extract data
    def read_excel_files(folder_path):
        excel_files = [file for file in os.listdir(folder_path) if file.endswith('.xlsx')]
        data = {}
        for file in excel_files:
            df = pd.read_excel(os.path.join(folder_path, file), sheet_name='Vulnérabilités')
            data[get_display_name(file)] = df[['Faille', 'Priorité', 'commentaire' ]]
        return data

    # Function to apply conditional formatting to the DataFrame
    def apply_priority_colors(df):
        def priority_color(row):
            if row['Priorité'] == 'P1':
                return ['background-color: #FF0000; color: white'] * len(row)
            elif row['Priorité'] == 'P2':
                return ['background-color: #ED7D31'] * len(row)
            elif row['Priorité'] == 'P3':
                return ['background-color: #FFC000'] * len(row)
            else:
                return [''] * len(row) 
        
        # Apply the function row-wise
        return df.style.apply(priority_color, axis=1)

    # Function to display selected Excel file data with styling
    def display_excel_data(selected_file, data):
        df = data[selected_file].rename(columns={'commentaire': 'Correctif'})

        df = df.dropna()  # Drop rows with NaN values

        # Apply priority color formatting
        styled_df = apply_priority_colors(df)

        # Use Pandas Styler to center headers and hide index
        styled_table = (
            styled_df.hide(axis='index')  # Hide the index
            .set_table_styles(
                [{'selector': 'th', 'props': [('text-align', 'center')]}]  # Center headers
            )
        )

        # Convert to HTML with additional spacing
        table_html = styled_table.to_html()
        centered_table_html = f"""
        <div style="margin-top: 20px;">
            {table_html}
        """
        
        # Display in Streamlit
        st.write(centered_table_html, unsafe_allow_html=True)

        

    # Function to get display name for the file
    def get_display_name(file_name):
        if file_name == 'GSK_Plan remediation_v1.1.xlsx':
            return 'HRA 7.40.60'
        elif file_name == 'HERMIONE-Plan_de_Remédiation(PTA8.3.0)-V1.0.xlsx':
            return 'Pléiades AGL3.3 PTA8.3.0 FP2'
        elif file_name == 'Plan de remédiation HIRIS-Audit_Sécurité_HRa_7.40.50 ed2 v5.1.xlsx':
            return 'HR Access 7.40.50 ed2'
        elif file_name == 'Plan_de_Remédiation_Conforama-4You-Pentest-V1.1.xlsx':
            return '4You 4.0'
        elif file_name == 'Plan_Remdéiation-AEGIDE-4You_7.2.0-V1.2.xlsx':
            return 'PROD / 4You 7.2.0'
        elif file_name == 'Plan_Remdéiation-CDC-HABITAT-HR4You-V1.0.xlsx':
            return '4You 7.1.0'
        elif file_name == 'Plan_Remédiation-FMM-4You 7.2_AGL 3.3.0-V1.0.xlsx':
            return '4You 7.2 / AGL 3.3.0'
        elif file_name == 'Plan_Remédiation-PICARD_PLe4You-V4.0.xlsx':
            return 'PLE 3.3  FP1/ 4YOU6.5.6'
        elif file_name == 'Plan-Remédiation_ARCUS-FEDAS_HRa_7.40.60 ed2-V1.1.xlsx':
            return 'HR Access 7.40.60 ed2'
        elif file_name == 'Plan-Remediation-JCARRION-GP4You-V1.0.xlsx':
            return 'GP4You 1.2.5 / GPIT 1.1.7'
        elif file_name == 'Remediation-Plan_BANKINTER_HRA-SPACE.xlsx':
            return '7.40.50 ed2'
        elif file_name == 'Remediation-Plan_OFECOMES-GP4YOU.xlsx':
            return 'GP4You'
        elif file_name == 'Remediation-Plan_SPUERKEESS_GP4You.xlsx':
            return 'GP4You  1.2.5 / GPIT 1.1.7'
        elif file_name == 'Remediation-Plan_SPUERKEESS_HRA.xlsx':
            return 'HRA  7.40.050 ed4'
        return file_name

    # Main function
    def main():
        # Folder path
        folder_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "plans de remédiation (1)")

        
        # Read Excel files and extract data
        data = read_excel_files(folder_path)
        
        # Filter to select Excel file
        selected_file = st.selectbox('Sélectionner la version', list(data.keys()))
        
        # Display data from selected Excel file without NaN values
        if selected_file:
            display_excel_data(selected_file, data)

    main()  # Call the main function to execute the app

    hide = """
    <style>
    #mainMenu {visibility:hidden;}
    footer {visibility:hidden;}
    header {visibility:hidden;}
    </style>
    """

    st.markdown(hide, unsafe_allow_html=True)
