import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
def app():
    selected_year = st.selectbox("Année:", options=[2024, 2025], index=1)
    # Load the Excel file
    sheet_name = f"Incident SOC {selected_year}"
    d = pd.read_excel('Suivi des incidents sécurité v 1.1.xlsx', sheet_name=sheet_name)
    d.columns = d.columns.str.lower().str.strip()
    d['status'].fillna('non traités', inplace=True)
    # Correct possible typos in the 'status' column
    d['status'] = d['status'].replace({'clôs': 'clos'})
    d['status'] = d['status'].replace({'en cours': 'en cours de vérification'})
    # Convert the 'date d'incident' column to string type
    d["date d'incident"] = d["date d'incident"].astype(str)
    # Clean the dates
    d["date d'incident"] = d["date d'incident"].str.replace('_x000D_', '').str.replace('00:00:00', '').str.strip()
    # Convert to datetime
    d["date d'incident"] = pd.to_datetime(d["date d'incident"], errors='coerce')
    # Filter data based on selected year
    d = d[d["date d'incident"].dt.year == selected_year]
    # Reset the index to maintain consecutive numbering
    d.reset_index(drop=True, inplace=True)
    # Fix locale issue: Use default locale or manually map months to French
    d['month'] = d["date d'incident"].dt.month_name(locale=None)  # Use default locale (English)
    # Alternatively, manually map to French month names if needed
    months_fr = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
    d['month'] = d["date d'incident"].dt.month - 1  # Adjust for 0-indexed months
    d['month'] = d['month'].apply(lambda x: months_fr[x] if x >= 0 else "")

    # Sidebar filters
    st.sidebar.header("Filtrer par:")
    valid_criticites = d["criticité"].dropna()
    valid_criticites = valid_criticites[valid_criticites.astype(str).str.strip().isin(['P1', 'P2', 'P3'])].unique()
    criticité = st.sidebar.multiselect("Filtrer par criticité:", options=valid_criticites, default=valid_criticites)

    status = st.sidebar.multiselect("Filtrer par statut:",
                                    options=d["status"].unique(),
                                    default=d["status"].unique())
    months = st.sidebar.multiselect("Filtrer par mois:",
                                    options=d["month"].unique(),
                                    default=d["month"].unique())

    # Apply filters
    selection_query = d.query("criticité == @criticité & status == @status & month in @months")

    # Display the main title
    st.title(f"Suivi des Bulletins {selected_year}")

    # Filter the DataFrame for 'P1', 'P2', and 'P3' priorities and count the occurrences
    P1_count = selection_query.loc[selection_query['criticité'] == 'P1'].shape[0]
    P2_count = selection_query.loc[selection_query['criticité'] == 'P2'].shape[0]
    P3_count = selection_query.loc[selection_query['criticité'] == 'P3'].shape[0]
    total_alerts = P1_count + P2_count + P3_count

    st.markdown(f"### Le nombre d'alertes SOC: {total_alerts}")

    first_column, second_column, third_column = st.columns(3)

    with first_column:
        st.markdown("### P1:")
        st.subheader(f'{P1_count}')
    with second_column:
        st.markdown("### P2:")
        st.subheader(f'{P2_count}')
    with third_column:
        st.markdown("### P3:")
        st.subheader(f'{P3_count}')


    st.markdown("_ _ _")

    # Count by Criticité and create a bar chart
    count_by_criticité = selection_query['criticité'].value_counts().reindex(['P1', 'P2', 'P3'], fill_value=0)

    # Create a layout with two columns
    col1, col2 = st.columns([1, 1])
    # Define custom colors for the charts
    custom_colors = {'P1': '#FF0000', 'P2': '#ED7D31', 'P3': '#FFC000'}  # Red for P1, Orange for P2, Yellow for P3
    status_colors = {'clos': '#2ca02c', 'en cours de vérification': '#7f7f7f', 'non traités': '#1f77b4'}

    # Bar chart in the left column
    with col1:
        # Create a modified dataframe with a taller P1 bar
        display_values = count_by_criticité.copy()

        # Find the maximum value to set a relative minimum height
        max_value = display_values.max()
        min_display_height = max(10, max_value * 0.15)  # At least 15% of the max height or 10, whichever is greater

        # Make P1 bars taller if they exist
        if 'P1' in display_values.index:
            # If P1 value is already large, leave it as is
            if display_values['P1'] < min_display_height:
                display_values['P1'] = min_display_height

        fig_bar = px.bar(display_values, x=display_values.index, y=display_values.values, title="Nombre de failles par criticité",
                        color=display_values.index, color_discrete_map=custom_colors, height=500)

        # Update x-axis properties
        fig_bar.update_xaxes(linecolor='black', tickfont=dict(color='black'), title_font=dict(color='black'))
        # Update y-axis properties
        fig_bar.update_yaxes(linecolor='black', tickfont=dict(color='black'), title_font=dict(color='black'), title_text='Nombre de failles')
        # Add text directly to the bars
        fig_bar.update_traces(
            text=count_by_criticité.values,  # Use original values for text
            textposition='inside',  # Position inside the bars
            textfont=dict(color='black', size=14),  # Make text visible
            insidetextanchor='middle'  # Center text in bars
        )

        st.plotly_chart(fig_bar, use_container_width=True)

    # Pie chart in the right column
    with col2:
        fig_pie = px.pie(count_by_criticité, values=count_by_criticité.values, names=count_by_criticité.index, color_discrete_sequence=[custom_colors[key] for key in count_by_criticité.index])
        fig_pie.update_traces(sort=False)  # Keep the legend order as P1, P2, P3
        st.plotly_chart(fig_pie, use_container_width=True)

    st.markdown("_ _ _")

    # Count by Criticité and status and create bar chart
    count_by_criticite_status = selection_query.groupby(['criticité', 'status']).size().reset_index(name='count')
    count_by_criticite_status['criticité'] = pd.Categorical(count_by_criticite_status['criticité'], categories=['P1', 'P2', 'P3'], ordered=True)

    # Create separate dataframes for total counts and status counts
    total_counts = count_by_criticité.reset_index()
    total_counts.columns = ['criticité', 'count']
    total_counts['status'] = 'Total'

    # Combine dataframes for plotting
    combined_counts = pd.concat([total_counts, count_by_criticite_status], ignore_index=True)

    # Create a combined bar chart
    fig_combined = go.Figure()

    # Find the maximum value to set a relative minimum height
    max_value = total_counts['count'].max()
    min_display_height = max(10, max_value * 0.15)  # At least 15% of the max height or 10, whichever is greater

    # Create modified data with taller P1 bars
    total_counts_display = total_counts.copy()

    # Set a minimum display value for P1 to make it more visible
    for i, row in total_counts_display.iterrows():
        if row['criticité'] == 'P1' and row['count'] < min_display_height:
            total_counts_display.at[i, 'count'] = min_display_height

    # Add total counts bars with modified heights
    fig_combined.add_trace(go.Bar(
        x=total_counts_display['criticité'],
        y=total_counts_display['count'],
        name='Total',
        marker_color=[custom_colors[crit] for crit in total_counts_display['criticité']],
        text=total_counts['count'],  # Use original values for text
        textposition='inside',  # Position inside the bars
        textfont=dict(color='black', size=14),  # Make text visible
        insidetextanchor='middle',  # Center text in bars
        showlegend=False  # Hide 'Total' from legend
    ))

    # Add status bars with modified heights for P1
    for status in count_by_criticite_status['status'].unique():
        status_data = count_by_criticite_status[count_by_criticite_status['status'] == status]
        status_data_display = status_data.copy()

        # Set minimum height for P1 bars
        for i, row in status_data_display.iterrows():
            if row['criticité'] == 'P1' and row['count'] < min_display_height:
                status_data_display.at[i, 'count'] = min_display_height

        fig_combined.add_trace(go.Bar(
            x=status_data_display['criticité'],
            y=status_data_display['count'],
            name=status,
            marker_color=status_colors.get(status, 'gray'),
            text=status_data['count'],  # Use original values for text
            textposition='inside',  # Position inside the bars
            textfont=dict(color='black', size=14),  # Make text visible
            insidetextanchor='middle'  # Center text in bars
        ))

    # Update layout
    fig_combined.update_layout(
        title="Nombre de failles par criticité et statut",
        barmode='group',
        legend_title_text='Statut',
        xaxis=dict(title='Criticité', linecolor='black', tickfont=dict(color='black'), title_font=dict(color='black')),
        yaxis=dict(title='Nombre de failles', linecolor='black', tickfont=dict(color='black'), title_font=dict(color='black')),
        height=500
    )

    st.plotly_chart(fig_combined, use_container_width=True)

    # Add a search box below the bar chart
    search_term = st.text_input("🔍 Rechercher dans le tableau :", "")

    # Apply all filters (sidebar + search) to the 'clos' table
    # First filter by sidebar selections
    filtered_data = selection_query.copy()
    
    # Then apply search filter
    if search_term:
        filtered_data = filtered_data[filtered_data.apply(
            lambda row: row.astype(str).str.contains(search_term, case=False, na=False).any(),
            axis=1
        )]
    
    # Filter only 'clos' status
    clos_table_data = filtered_data[filtered_data['status'] == 'clos'][['incident id', 'description', 'criticité', 'ticket jira']]

    # Display the filtered 'clos' table
    if not clos_table_data.empty:
        # Format the 'ticket jira' column as clickable links
        def make_clickable(url):
            if pd.isna(url):  # Handle NaN values
                return ""
            return f'<a href="{url}" target="_blank">{url}</a>'

        # Create a display copy to preserve original data for download
        display_table = clos_table_data.copy()
        if 'ticket jira' in display_table.columns:
            display_table['ticket jira'] = display_table['ticket jira'].apply(make_clickable)
        
        # Use Pandas Styler to format table
        styled_table = (
            display_table.style
            .hide(axis='index')  # Hide the index
            .set_table_styles(
                [{'selector': 'th', 'props': [('text-align', 'center')]}]  # Center headers
            )
        )

        # Create columns for title and toggle
        col1, col2 = st.columns([0.8, 0.2])
        with col1:
            st.subheader("Détails des failles clos")
        with col2:
            # Use direct toggle value for immediate update
            show_table = st.toggle("Afficher", value=True, key="toggle_table")
                
        # Only show the table if the toggle is on
        if show_table:
            table_html = styled_table.to_html()
            centered_table_html = f"""
            <div style="margin-top: 20px;">
                {table_html}
      
            """
            st.write(centered_table_html, unsafe_allow_html=True)

        # Add download button for visible Excel data
        from io import BytesIO
        output = BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            clos_table_data.to_excel(writer, sheet_name='Détails des failles clos', index=False)
        excel_data = output.getvalue()



    # Keep the original download button for the entire file
    with open("Suivi des incidents sécurité v 1.1.xlsx", "rb") as f:
        st.download_button(
            label="📥 Télécharger le fichier Excel",
            data=f,
            file_name="Suivi_incidents_securite_complet.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )







    hide = """
    <style>
    #mainMenu {visibility:hidden;}
    footer {visibility:hidden;}
    header {visibility:hidden;}
    </style>
    """

    st.markdown(hide, unsafe_allow_html=True)
