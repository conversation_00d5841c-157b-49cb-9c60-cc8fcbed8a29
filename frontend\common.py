from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from webdriver_manager.microsoft import EdgeChromiumDriverManager  # Alternative manager

def create_webdriver():
    # Setting up Edge options
    edge_options = Options()
    edge_options.add_argument("--headless")  # Run browser in headless mode
    edge_options.add_argument("--no-sandbox")
    edge_options.add_argument("--disable-dev-shm-usage")
    
    # Use EdgeChromiumDriverManager to install the Edge driver
    driver = webdriver.Edge(service=Service(EdgeChromiumDriverManager().install()), options=edge_options)
    return driver
