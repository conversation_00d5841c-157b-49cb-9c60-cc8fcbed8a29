import oracledb
import streamlit as st
import os
def get_connection():
    """
    Get a connection to the Oracle database
    Returns a connection object
    """
    try:
        # Direct connection to Oracle using oracledb
        conn = oracledb.connect(
            user="PRODOPSSEC",
            password="1Hjksdb797MMP",
            dsn="FRSOPSLBDD084-VIP.SOPRAHRONLINE.SOPRA:1523/ANEXDEV"
        )

        return conn
    except oracledb.DatabaseError as e:
        error, = e.args
        st.error(f"Oracle Database error: {error.message}")
        raise
    except Exception as e:
        st.error(f"Database connection error: {str(e)}")
        raise

def update_row(conn, table_name, row_data, primary_key_col):
    """
    Update an existing row in the database

    Args:
        conn: Oracle connection object
        table_name: Name of the table to update
        row_data: Dictionary containing column names and values
        primary_key_col: Name of the primary key column

    Returns:
        Tuple (success, error_message):
        - success: True if successful, False otherwise
        - error_message: Error message if unsuccessful, empty string otherwise
    """
    try:
        cursor = conn.cursor()

        # Filter out None values and convert empty strings to None for database
        filtered_row_data = {}
        for key, value in row_data.items():
            if value == "":
                filtered_row_data[key] = None
            else:
                filtered_row_data[key] = value

        # Get the primary key value
        primary_key_value = filtered_row_data.get(primary_key_col)
        if primary_key_value is None:
            return False, f"Primary key value for {primary_key_col} is missing"

        # Build column list and value list for SET clause (excluding primary key)
        update_columns = [col for col in filtered_row_data.keys() if col != primary_key_col]
        update_values = [filtered_row_data[col] for col in update_columns]

        # If there are no columns to update, return success
        if not update_columns:
            return True, ""

        # Build SET clause with positional bind variables
        set_clause_parts = []
        for i, col in enumerate(update_columns):
            set_clause_parts.append(f'"{col}" = :{i+1}')
        set_clause = ", ".join(set_clause_parts)

        # Add primary key value to the end of values list
        update_values.append(primary_key_value)

        # Build SQL statement
        sql = f'UPDATE "{table_name}" SET {set_clause} WHERE "{primary_key_col}" = :{len(update_values)}'

        print(f"UPDATE SQL: {sql}")
        print(f"UPDATE Values: {update_values}")

        # Execute the update with positional parameters
        cursor.execute(sql, update_values)
        conn.commit()

        return True, ""
    except Exception as e:
        error_msg = f"Error updating row: {e}"
        print(error_msg)
        return False, error_msg
    finally:
        if cursor:
            cursor.close()

def insert_row(conn, table_name, row_data):
    """
    Insert a new row into the database

    Args:
        conn: Oracle connection object
        table_name: Name of the table to insert into
        row_data: Dictionary containing column names and values

    Returns:
        Tuple (success, error_message):
        - success: True if successful, False otherwise
        - error_message: Error message if unsuccessful, empty string otherwise
    """
    try:
        cursor = conn.cursor()

        # Convert empty strings to None for database and filter out None values
        filtered_row_data = {}
        for key, value in row_data.items():
            if value == "":
                # For empty strings, set to None (NULL in database)
                filtered_row_data[key] = None
            elif value is not None:
                # Keep non-None values
                filtered_row_data[key] = value

        # If no valid data after filtering, return success (nothing to insert)
        if not filtered_row_data:
            print("No valid data to insert after filtering")
            return True, ""

        # Try a simpler approach for Level=Level2 case
        if 'Level' in filtered_row_data and filtered_row_data['Level'] == 'Level2':
            try:
                # First, get the actual column names from the database
                cursor.execute('SELECT * FROM "NEWTABLE" WHERE ROWNUM <= 1')
                col_names = [d[0] for d in cursor.description]
                print(f"Actual column names: {col_names}")

                # Find the priority column (case-insensitive)
                priority_col = None
                for col in col_names:
                    if col.upper() == 'PRIORITE' or col.upper() == 'PRIORITY' or 'PRIOR' in col.upper():
                        priority_col = col
                        break

                if priority_col:
                    # Use the actual column name
                    sql = f'INSERT INTO "NEWTABLE" ("Level", "IMPACT", "{priority_col}") VALUES (:1, :2, :3)'
                    values = ['Level2', filtered_row_data.get('IMPACT', None), filtered_row_data.get(priority_col, 'P1')]
                else:
                    # Try without the priority column
                    sql = 'INSERT INTO "NEWTABLE" ("Level", "IMPACT") VALUES (:1, :2)'
                    values = ['Level2', filtered_row_data.get('IMPACT', None)]

                print(f"Using simplified SQL: {sql}")
                print(f"With values: {values}")

                cursor.execute(sql, values)
                conn.commit()
                return True, ""
            except Exception as e:
                print(f"Simplified approach failed: {e}")
                # Continue with the normal approach

        # Build column list and value list
        columns = list(filtered_row_data.keys())
        values = list(filtered_row_data.values())

        # Build SQL statement with positional bind variables
        # Escape column names with double quotes to handle case sensitivity and special characters
        columns_str = ", ".join([f'"{col}"' for col in columns])
        placeholders = ", ".join([f":{i+1}" for i in range(len(columns))])

        # Build SQL statement
        sql = f'INSERT INTO "{table_name}" ({columns_str}) VALUES ({placeholders})'

        print(f"SQL: {sql}")
        print(f"Values: {values}")

        # Execute the insert with positional parameters
        cursor.execute(sql, values)
        conn.commit()

        return True, ""
    except Exception as e:
        error_msg = f"Error inserting row: {e}"
        print(error_msg)
        return False, error_msg
    finally:
        if cursor:
            cursor.close()

def check_value_exists(conn, table_name, column_name, value):
    """
    Check if a value already exists in a specific column of a table

    Args:
        conn: Oracle connection object
        table_name: Name of the table
        column_name: Name of the column to check
        value: Value to check for

    Returns:
        Tuple (exists, error_message):
        - exists: True if value exists, False otherwise
        - error_message: Error message if an error occurred, empty string otherwise
    """
    cursor = None
    try:
        cursor = conn.cursor()

        # Build SQL statement with positional bind variable
        sql = f'SELECT COUNT(*) FROM "{table_name}" WHERE "{column_name}" = :1'

        # Execute the query with positional parameter
        cursor.execute(sql, [value])
        count = cursor.fetchone()[0]

        return count > 0, ""
    except Exception as e:
        error_msg = f"Error checking if value exists: {e}"
        print(error_msg)
        return False, error_msg
    finally:
        if cursor:
            try:
                cursor.close()
            except Exception as close_e:
                print(f"Error closing cursor in check_value_exists: {close_e}")

def get_table_structure(conn, table_name):
    """
    Get the structure of a table including column names and data types

    Args:
        conn: Oracle connection object
        table_name: Name of the table

    Returns:
        List of dictionaries with column information
    """
    try:
        cursor = conn.cursor()

        # Query to get column information
        sql = """
        SELECT column_name, data_type, nullable
        FROM user_tab_columns
        WHERE UPPER(table_name) = :1
        ORDER BY column_id
        """

        cursor.execute(sql, [table_name.upper()])
        columns = []

        for row in cursor:
            columns.append({
                'name': row[0],
                'type': row[1],
                'nullable': row[2]
            })

        # Get primary key information
        sql_pk = """
        SELECT cols.column_name
        FROM all_constraints cons, all_cons_columns cols
        WHERE cons.constraint_type = 'P'
        AND cons.constraint_name = cols.constraint_name
        AND cons.owner = cols.owner
        AND UPPER(cols.table_name) = :1
        """

        try:
            cursor.execute(sql_pk, [table_name.upper()])
            pk_columns = [row[0] for row in cursor]

            # Mark primary key columns
            for col in columns:
                if col['name'] in pk_columns:
                    col['is_primary_key'] = True
                else:
                    col['is_primary_key'] = False

            print(f"Primary key columns for {table_name}: {pk_columns}")
        except Exception as e:
            print(f"Error getting primary key information: {e}")
            # Continue even if we can't get PK info

        return columns
    except Exception as e:
        print(f"Error getting table structure: {e}")
        return []
    finally:
        if cursor:
            cursor.close()

def get_exact_column_names(conn, table_name):
    """
    Get the exact column names from the database

    Args:
        conn: Oracle connection object
        table_name: Name of the table

    Returns:
        List of column names with exact case
    """
    cursor = None
    try:
        cursor = conn.cursor()
        cursor.execute(f'SELECT * FROM "{table_name}" WHERE ROWNUM <= 1')
        col_names = [d[0] for d in cursor.description]
        return col_names
    except Exception as e:
        print(f"Error getting exact column names: {e}")
        return []
    finally:
        if cursor:
            try:
                cursor.close()
            except Exception as close_e:
                print(f"Error closing cursor in get_exact_column_names: {close_e}")

def get_primary_key_columns(conn, table_name):
    """
    Get the primary key columns of a table

    Args:
        conn: Oracle connection object
        table_name: Name of the table

    Returns:
        List of primary key column names
    """
    try:
        cursor = conn.cursor()

        # Query to get primary key columns
        sql = """
        SELECT cols.column_name
        FROM all_constraints cons, all_cons_columns cols
        WHERE cons.constraint_type = 'P'
        AND cons.constraint_name = cols.constraint_name
        AND cons.owner = cols.owner
        AND UPPER(cols.table_name) = :1
        """

        cursor.execute(sql, [table_name.upper()])
        pk_columns = [row[0] for row in cursor]

        return pk_columns
    except Exception as e:
        print(f"Error getting primary key columns: {e}")
        return []
    finally:
        if cursor:
            cursor.close()

def test_connection():
    """
    Test the Oracle database connection

    Returns:
        Tuple (success, message):
        - success: True if connection successful, False otherwise
        - message: Success or error message
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # Test a simple query
        cursor.execute("SELECT 1 FROM DUAL")
        result = cursor.fetchone()

        # Test getting table names
        cursor.execute("""
            SELECT table_name
            FROM user_tables
            ORDER BY table_name
        """)
        tables = [row[0] for row in cursor.fetchall()]

        # Get version information
        cursor.execute("SELECT BANNER FROM V$VERSION")
        version_info = cursor.fetchone()[0]

        cursor.close()
        conn.close()

        return True, f"Connection successful! Oracle version: {version_info}. Found {len(tables)} tables."
    except oracledb.DatabaseError as e:
        error, = e.args
        return False, f"Oracle Database error: {error.message}"
    except Exception as e:
        return False, f"Connection error: {str(e)}"
