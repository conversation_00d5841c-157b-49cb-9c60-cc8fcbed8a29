stages:
  - build
  - deploy

variables:
  DOCKER_IMAGE_BACKEND: "$CI_REGISTRY_IMAGE/backend"
  DOCKER_IMAGE_FRONTEND: "$CI_REGISTRY_IMAGE/frontend"

# 🔹 BUILD BACKEND (FastAPI)
build-backend:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $DOCKER_IMAGE_BACKEND:latest -f backend/Dockerfile backend/
    - docker push $DOCKER_IMAGE_BACKEND:latest

# 🔹 BUILD FRONTEND (Streamlit)
build-frontend:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $DOCKER_IMAGE_FRONTEND:latest -f frontend/Dockerfile frontend/
    - docker push $DOCKER_IMAGE_FRONTEND:latest

# 🔹 DEPLOY CONTAINERS
deploy:
  stage: deploy
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker pull $DOCKER_IMAGE_BACKEND:latest
    - docker pull $DOCKER_IMAGE_FRONTEND:latest
    - docker run -d -p 8000:8000 --name backend $DOCKER_IMAGE_BACKEND:latest
    - docker run -d -p 8501:8501 --name frontend $DOCKER_IMAGE_FRONTEND:latest
  environment: production
