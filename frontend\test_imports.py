#!/usr/bin/env python3
"""
Test script to verify that all required modules can be imported correctly.
This helps debug import issues in different environments.
"""

import os
import sys
import importlib.util

def test_import(module_name, file_path):
    """Test importing a module from a file path."""
    print(f"\n=== Testing {module_name} ===")
    print(f"File path: {file_path}")
    print(f"File exists: {os.path.exists(file_path)}")
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        # Try to import using importlib
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        if spec is None:
            print(f"❌ Could not create spec for {module_name}")
            return False
            
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print(f"✅ Successfully imported {module_name}")
        
        # Check if main function exists
        if hasattr(module, 'main'):
            print(f"✅ main() function found in {module_name}")
        else:
            print(f"⚠️ main() function NOT found in {module_name}")
            
        return True
        
    except Exception as e:
        print(f"❌ Failed to import {module_name}: {e}")
        return False

def main():
    print("=== Module Import Test ===")
    print(f"Python version: {sys.version}")
    print(f"Current working directory: {os.getcwd()}")
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"Script directory: {script_dir}")
    
    # List all files in the directory
    try:
        files = os.listdir(script_dir)
        print(f"Files in directory: {files}")
    except Exception as e:
        print(f"Error listing directory: {e}")
    
    # Test each module
    modules_to_test = [
        ("testingcontre", os.path.join(script_dir, "testingcontre.py")),
        ("testingCERT", os.path.join(script_dir, "testingCERT.py")),
        ("testingSOC", os.path.join(script_dir, "testingSOC.py"))
    ]
    
    results = []
    for module_name, file_path in modules_to_test:
        success = test_import(module_name, file_path)
        results.append((module_name, success))
    
    print("\n=== Summary ===")
    for module_name, success in results:
        status = "✅ OK" if success else "❌ FAILED"
        print(f"{module_name}: {status}")
    
    # Test database import
    print("\n=== Testing database module ===")
    try:
        import database
        print("✅ Database module imported successfully")
        
        # Test database connection
        try:
            conn = database.get_connection()
            print("✅ Database connection successful")
            conn.close()
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            
    except Exception as e:
        print(f"❌ Database module import failed: {e}")

if __name__ == "__main__":
    main()
