import streamlit as st
from datetime import datetime, timedelta
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import os
import re
def normalize_col_name(name):
    return re.sub(r'\s+', '', name.lower().strip())

def transform_statut(value):
    normalized = re.sub(r'\s+', '', str(value).lower())
    if any(x in normalized for x in ['clos', 'corrigée', 'corrigé', 'clôs']):
        return 'Corrigée'
    elif 'encours' in normalized:
        return 'En cours'
    else:
        return value

def clean_team_column(df, col):
    df[col] = df[col].str.strip().str.lower().replace({
        'tma interne': 'TMA',
        'séc.op': 'PRODOPS',
        'sec.op': 'PRODOPS',
        'prodops': 'PRODOPS',
        'tma': 'TMA',
        'r&d': 'R&D',
        'pôle sécurité': 'PRODOPS',
        'r&d / prodops': 'R&D / PRODOPS',
        'r&d et prodops': 'R&D / PRODOPS'
    })
    return df

def app():
    # CSS for styling
    st.markdown(
        f"""
        <style>
        header, footer {{visibility: hidden;}}
        .reportview-container {{
            background-color: #ebeff3;
        }}
        .main {{ 
            background-color: #ebeff3;
            padding: 0px;
            margin: 0px;
        }}
        .box {{
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 3px 2px 10px rgba(0, 0, 0, 0.1);
            margin: 10px;
            text-align: center;
        }}
        .header {{
            font-size: 24px;
            font-weight: bold;
            text-align: center;
        }}
        .subheader {{
            font-size: 20px;
            text-align: center.
        }}
        .stat {{
            font-size: 28px;
            font-weight: bold;
            text-align: center.
        }}
        .chart-container {{
            display: flex;
            flex-direction: column.
            align-items: center.
        }}
        .info-box {{
            display: flex.
            justify-content: space-around.
            align-items: center.
            padding-top: 10px.
        }}
        .info-box div {{
            text-align: center.
        }}
        </style>
        """,
        unsafe_allow_html=True,
    )

    ## Same Code: Year and Month Selection
    available_years = [2024, 2025]  # Adjust as needed

    col_year, col_month = st.columns(2)
    with col_year:
        selected_year = st.selectbox("Année :", available_years, index=1)

    current_year = datetime.now().year
    current_month = datetime.now().month

    months = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
    month_translation = {
        "Tous les mois": "Tous les mois",  
        "Janvier": "Janv.", "Février": "Févr", "Mars": "Mars", "Avril": "Avr",
        "Mai": "Mai", "Juin": "Juin", "Juillet": "Juil", "Août": "Août",
        "Septembre": "Sept", "Octobre": "Oct", "Novembre": "Nov", "Décembre": "Déc"
    }

    if selected_year == current_year:
        available_months = ["Tous les mois"] + months[:current_month]
    else:
        available_months = ["Tous les mois"] + months

    with col_month:
        selected_month = st.selectbox("Mois :", available_months)

    selected_month_translated = month_translation[selected_month]

    ## Updated Code: Filter data for Bulletins CERT
    sheet_name = f"suivi {selected_year}"
    d_cert = pd.read_excel('Suivi Bulletins CERT V1.1.xlsx', sheet_name=sheet_name, header=6)
    d_cert.columns = d_cert.columns.str.lower().str.strip()
    d_cert['date du bulletin'] = d_cert['date du bulletin'].astype(str)
    d_cert['date du bulletin'] = d_cert['date du bulletin'].str.replace('_x000D_', '').str.replace('00:00:00', '').str.strip()

    d_cert['date du bulletin'] = pd.to_datetime(d_cert['date du bulletin'], errors='coerce')
    d_cert = d_cert[d_cert['date du bulletin'].dt.year == selected_year]
    d_cert['month'] = d_cert['date du bulletin'].dt.month - 1
    months_fr = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
    d_cert['month'] = d_cert['month'].apply(lambda x: months_fr[x] if x >= 0 else "")



    
    if selected_month == "Tous les mois":
        filtered_d_cert = d_cert
    else:
        filtered_d_cert = d_cert[d_cert['month'] == selected_month]

    ## Updated Code: Summarize data for Bulletins CERT
    if selected_month == "Tous les mois":
        summary_data = filtered_d_cert.groupby('criticité').size().reset_index(name='count')
        labels_cert = summary_data['criticité'].tolist()
        values_cert = summary_data['count'].tolist()
    else:
        labels_cert = filtered_d_cert['criticité'].value_counts().index.tolist()
        values_cert = filtered_d_cert['criticité'].value_counts().tolist()
    colors_cert = ['#FF0000', '#ED7D31', '#FFC000']

    ## Updated Code: Dynamically handle SOC Data
    sheet_name = f"Incident SOC {selected_year}"
    d_soc = pd.read_excel('Suivi des incidents sécurité v 1.1.xlsx', sheet_name=sheet_name)
    d_soc.columns = d_soc.columns.str.lower().str.strip()
    d_soc["date d'incident"] = d_soc["date d'incident"].astype(str)
    d_soc["date d'incident"] = d_soc["date d'incident"].str.replace('_x000D_', '').str.replace('00:00:00', '').str.strip()
    d_soc["date d'incident"] = pd.to_datetime(d_soc["date d'incident"], errors='coerce')
    d_soc = d_soc[d_soc["date d'incident"].dt.year == selected_year]
    d_soc['month'] = d_soc["date d'incident"].dt.month_name(locale=None)
    # Manually map months to French
    months_fr = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]

    # Replace English month names with French ones (adjusting for 0-indexed months)
    d_soc['month'] = d_soc["date d'incident"].dt.month - 1
    d_soc['month'] = d_soc['month'].apply(lambda x: months_fr[x] if x >= 0 else "")

    if selected_month == "Tous les mois":
        filtered_d_soc = d_soc
    else:
        filtered_d_soc = d_soc[d_soc['month'] == selected_month]

    if selected_month == "Tous les mois":
        count_by_criticité_soc = (
            filtered_d_soc.groupby('criticité')
            .size()
            .reindex(['P1', 'P2', 'P3'], fill_value=0)
            .reset_index(name='count')
        )
    else:
        count_by_criticité_soc = (
            filtered_d_soc['criticité']
            .value_counts()
            .reindex(['P1', 'P2', 'P3'])
            .reset_index()
            .dropna()
        )
        count_by_criticité_soc.columns = ['criticité', 'count']

    count_by_criticité_soc['criticité'] = pd.Categorical(count_by_criticité_soc['criticité'], categories=['P1', 'P2', 'P3'], ordered=True)








    # Third count calculated dynamically
    # root_folder_path = r'C:\Users\<USER>\Downloads\project-main\project-main\contre-analyse'
    root_folder_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "project-main", "contre-analyse")




        ## Updated Code: Handle Plan Remediation Integration
    column_mappings = {
        "vulnérabilité": ["vulnérabilité", "vulnerability", "faille"],
        "priorité": ["priorité", "priority", "criticité", "criticity"],
        "équipe en charge": ["équipe en charge", "equipe en charge", "équipe encharge",
                              "equipe encharge", "equipe projet", "intervenant", "équipe responsable"],
        "statut": ["statut", "statuts", "status"],
    }

    def find_excel_files(root_folder):
        excel_files = []
        for dirpath, _, filenames in os.walk(root_folder):
            if 'R&D' in dirpath:
                continue
            for file in filenames:
                if file.endswith(".xlsx"):
                    excel_files.append(os.path.join(dirpath, file))
        return excel_files

    def find_column(df, possible_names):
        normalized_possible = [normalize_col_name(name) for name in possible_names]
        for col in df.columns:
            if normalize_col_name(col) in normalized_possible:
                return col
        return None


    def load_restitution_date(file_path):
        sheet_names = ["Déroulement", "Schedule"]
        for sheet in sheet_names:
            try:
                df = pd.read_excel(file_path, sheet_name=sheet, skiprows=lambda x: x > 10)
                df = df.dropna(how='all').dropna(axis=1, how='all')
                df.columns = df.iloc[0]
                df = df[1:].reset_index(drop=True)
                if 'Restitution' in df.columns:
                    first_date = df['Restitution'].dropna().iloc[0]
                    if isinstance(first_date, (int, float)):
                        first_date = pd.to_datetime("1899-12-30") + pd.to_timedelta(first_date, "D")
                    elif isinstance(first_date, str):
                        try:
                            cleaned_date = re.sub(r'^[A-Za-zéû]+\s+', '', first_date).strip()
                            first_date = pd.to_datetime(cleaned_date, dayfirst=True)
                        except ValueError:
                            return []
                    return [first_date.strftime("%d/%m/%Y")]
            except Exception:
                continue
        return []

    def extract_years_and_months_from_restitution_dates(restitution_dates):
        years_months = []
        for date_str in restitution_dates:
            try:
                dt = datetime.strptime(date_str, '%d/%m/%Y')
                years_months.append((dt.year, dt.month))
            except Exception:
                continue
        return sorted(set(years_months))

    def calculate_deadline(row, restitution_dates, priorite_col):
        restitution_date = restitution_dates[0] if restitution_dates else None
        if restitution_date:
            restitution_date = datetime.strptime(restitution_date, '%d/%m/%Y')
            if row[priorite_col] == 'P1':
                deadline_date = restitution_date + timedelta(days=30)
            elif row[priorite_col] == 'P2':
                deadline_date = restitution_date + timedelta(days=60)
            elif row[priorite_col] == 'P3':
                deadline_date = restitution_date + timedelta(days=180)
            else:
                deadline_date = None
            if deadline_date:
                return deadline_date.strftime('%d/%m/%Y')
        return None

    def calculate_deadline_compliance(row, statut_col):
        if row[statut_col] == 'non corrigée':
            try:
                deadline_date = datetime.strptime(row['Deadline'], '%d/%m/%Y')
                return 'En attente' if deadline_date > datetime.now() else 'Manqué'
            except Exception:
                return None
        return None

    def load_data(file_path, restitution_dates):
        try:
            df = pd.read_excel(file_path, sheet_name='Vulnérabilités')
           
        except ValueError:
            try:
                df = pd.read_excel(file_path, sheet_name='Vulnerabilities')
               
            except ValueError:
                st.warning(f"Sheet 'Vulnérabilités' not found in {os.path.basename(file_path)}. Skipping file.")
                return pd.DataFrame()

        df.columns = df.columns.str.strip().str.replace(r'\s+', ' ', regex=True)

        df['source_file'] = os.path.basename(file_path)
        statut_col = find_column(df, column_mappings["statut"])
        priorite_col = find_column(df, column_mappings["priorité"])
        if not priorite_col:
            return pd.DataFrame()  # skip file if priorité column is missing

        equipe_col = find_column(df, column_mappings["équipe en charge"])

        if not equipe_col or not priorite_col:
            return pd.DataFrame()  # skip this file silently

        priorite_col = find_column(df, column_mappings["priorité"])

        if statut_col:
            df[statut_col] = df[statut_col].apply(transform_statut)

        if equipe_col:
            df = clean_team_column(df, equipe_col)

        df['Deadline'] = df.apply(lambda row: calculate_deadline(row, restitution_dates, priorite_col), axis=1)
        if statut_col:
            df['Conformité des délais'] = df.apply(lambda row: calculate_deadline_compliance(row, statut_col), axis=1)
        else:
            df['Conformité des délais'] = None
        return df


    def render_remediation_chart(df):
        priorite_col = "priorité"

        count_by_priorite = (
            df[priorite_col]
            .value_counts()
            .reindex(['P1', 'P2', 'P3'], fill_value=0)
            .reset_index()
        )
        count_by_priorite.columns = [priorite_col, 'count']
        count_by_priorite[priorite_col] = pd.Categorical(
            count_by_priorite[priorite_col],
            categories=['P1', 'P2', 'P3'],
            ordered=True
        )
        count_by_priorite = count_by_priorite.sort_values(by=priorite_col)
        custom_colors = {'P1': '#FF0000', 'P2': '#ED7D31', 'P3': '#FFC000'}
        fig_bar = px.bar(
            count_by_priorite,
            x=priorite_col,
            y='count',
           
            text_auto=True,
            color=priorite_col,
            color_discrete_map=custom_colors,
            height=500
        )
        fig_bar.update_xaxes(title="", linecolor='black', tickfont=dict(color='black'))
        fig_bar.update_yaxes(title="Nombre de failles", linecolor='black', tickfont=dict(color='black'))
        fig_bar.update_layout(legend_title_text='Priorité')
        st.plotly_chart(fig_bar, use_container_width=True)

    # ---------------------- Plans de Remédiation Section ---------------------- #

    def plans_remediation_section():
        # Force the year to 2024
        base_year = 2024
        script_dir = os.path.dirname(os.path.abspath(__file__))
        folder_path = os.path.join(script_dir, "plansremediationsSansOneDrive")
        
        all_files = find_excel_files(folder_path)
        if not all_files:
            st.warning("Aucun fichier Excel trouvé (ou dossier R&D ignoré).")
            return

        # Gather restitution dates from all files
        all_dates = []
        for f in all_files:
            all_dates.extend(load_restitution_date(f))
        years_months = extract_years_and_months_from_restitution_dates(all_dates)
        
        month_name_to_number = {
            "Janvier": 1, "Février": 2, "Mars": 3, "Avril": 4,
            "Mai": 5, "Juin": 6, "Juillet": 7, "Août": 8,
            "Septembre": 9, "Octobre": 10, "Novembre": 11, "Décembre": 12
        }


        
        # Filter months for the selected year
        available_months = sorted({month for year, month in years_months if year == selected_year})
        if not available_months:
            st.warning(f"Aucun mois disponible pour {selected_year}.")
            return
        month_mapping = {
            1: "Janvier", 2: "Février", 3: "Mars", 4: "Avril", 5: "Mai",
            6: "Juin", 7: "Juillet", 8: "Août", 9: "Septembre",
            10: "Octobre", 11: "Novembre", 12: "Décembre"
        }



        filtered_files = []
        for f in all_files:
            for date in load_restitution_date(f):
                try:
                    dt = datetime.strptime(date, '%d/%m/%Y')
                    if dt.year == selected_year and (
                        selected_month == "Tous les mois" or dt.month == month_name_to_number[selected_month]
                    ):
                        filtered_files.append(f)
                        break
                except Exception:
                    continue
        st.markdown(f"<div class='box'><div class='header'>Plans de Remédiation: {len(filtered_files)}</div></div>", unsafe_allow_html=True)


        if filtered_files:
            dfs = [load_data(f, load_restitution_date(f)) for f in filtered_files]
            dfs = [df for df in dfs if not df.empty]
            df_merged = pd.concat(dfs, ignore_index=True)

            priorite_col = find_column(df_merged, column_mappings["priorité"])
            if priorite_col:
                # Normalize and clean priorité column
                df_merged[priorite_col] = df_merged[priorite_col].astype(str).str.strip().str.upper()
                df_merged = df_merged[df_merged[priorite_col].isin(['P1', 'P2', 'P3'])]

                # Count by priorité
                count_by_priorite = (
                    df_merged[priorite_col]
                    .value_counts()
                    .reindex(['P1', 'P2', 'P3'], fill_value=0)
                    .reset_index()
                )
                count_by_priorite.columns = [priorite_col, 'count']
                count_by_priorite[priorite_col] = pd.Categorical(count_by_priorite[priorite_col], categories=['P1','P2','P3'], ordered=True)
                count_by_priorite = count_by_priorite.sort_values(by=priorite_col)

                custom_colors = {'P1': '#FF0000', 'P2': '#ED7D31', 'P3': '#FFC000'}
                fig_bar = px.bar(
                    count_by_priorite,
                    x=priorite_col,
                    y='count',
                    title="Nombre de failles par priorité",
                    text_auto=True,
                    color=priorite_col,
                    color_discrete_map=custom_colors,
                    height=500
                )
                fig_bar.update_xaxes(title="", linecolor='black', tickfont=dict(color='black'))
                fig_bar.update_yaxes(title="Nombre de failles", linecolor='black', tickfont=dict(color='black'))
                fig_bar.update_layout(legend_title_text='Priorité')
                st.plotly_chart(fig_bar, use_container_width=True)

        else:
            st.warning(f"Aucun fichier trouvé pour {selected_year} et {month_mapping.get(selected_month, selected_month)}.")
    ## Scans Qualys
    script_dir = os.path.dirname(os.path.abspath(__file__))
    excel_file_scan = os.path.join(script_dir, f"palnning des scan Qualys {selected_year} V1.1.xlsx")

    sheet_name_scan = 'SYNTHESE'

    # Read the Excel file
    df_scan = pd.read_excel(excel_file_scan, sheet_name=sheet_name_scan, header=1)
    df_subset_scan = df_scan.iloc[0:6]

    # Select columns 4 to 16 (monthly data)
    columns_of_interest_scan = df_scan.columns[4:16]
    df_months_scan = df_subset_scan[columns_of_interest_scan]

    # Extract actual scan counts
    df_actual_scan = df_months_scan.copy()

    # Calculate percentages
    df_percentages_scan = df_months_scan.div(df_subset_scan['NB Scan Planifiés'], axis=0) * 100
    df_percentages_scan = df_percentages_scan.round(0)

    # Prepare the data for Plotly
    df_percentages_transposed_scan = df_percentages_scan.T
    df_actual_transposed_scan = df_actual_scan.T

    df_percentages_transposed_scan.columns = df_subset_scan.iloc[:, 0]
    df_actual_transposed_scan.columns = df_subset_scan.iloc[:, 0]

    df_percentages_transposed_scan['Month'] = df_percentages_transposed_scan.index
    df_actual_transposed_scan['Month'] = df_actual_transposed_scan.index

    # Melt DataFrames for Plotly
    df_melted_percentage_scan = df_percentages_transposed_scan.melt(id_vars='Month', var_name='Label', value_name='Percentage')
    df_melted_actual_scan = df_actual_transposed_scan.melt(id_vars='Month', var_name='Label', value_name='Actual')

    # Merge both datasets
    df_melted_scan = pd.merge(df_melted_percentage_scan, df_melted_actual_scan, on=['Month', 'Label'])

    # Filter based on the selected month
    if selected_month == "Tous les mois":
        df_filtered_scan = df_melted_scan
    else:
        df_filtered_scan = df_melted_scan[df_melted_scan['Month'] == selected_month_translated]

    # Create the bar chart with Plotly
    fig_scan = go.Figure()

    for label in df_filtered_scan['Label'].unique():
        if label == 'GLOBAL':
            fig_scan.add_trace(go.Scatter(
                x=df_filtered_scan[df_filtered_scan['Label'] == label]['Month'],
                y=df_filtered_scan[df_filtered_scan['Label'] == label]['Percentage'],
                mode='lines+markers',
                name=label,
                hovertemplate='%{y:.0f}%<extra></extra>'
            ))
        else:
            fig_scan.add_trace(go.Bar(
                x=df_filtered_scan[df_filtered_scan['Label'] == label]['Month'],
                y=df_filtered_scan[df_filtered_scan['Label'] == label]['Percentage'],
                text=df_filtered_scan[df_filtered_scan['Label'] == label]['Actual'],  # Show actual scan counts inside bars
                textposition='inside',  # Position inside the bars
                name=label,
                hovertemplate='%{y:.0f}% (%{text})<extra></extra>'  # Show % + actual number on hover
            ))

    # Update layout
    fig_scan.update_layout(
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=-0.3,
            xanchor="center",
            x=0.5
        ),
        yaxis=dict(
            tickvals=[0, 20, 40, 60, 80, 100],
            ticktext=["0%", "20%", "40%", "60%", "80%", "100%"],
            title="Pourcentage"
        ),
        barmode='group',
        height=450,
        width=450
    )


    ## Same Code: Create and display dashboard layout
    col1, col2 = st.columns((1, 1))  
    col3 = st.columns((1,))
    col4, col5 = st.columns((1, 1)) 

    with col1:
        
        total_cert_count = sum(values_cert)  # Sum of all criticité counts
        st.markdown(f"<div class='box'><div class='header'>Bulletins CERT: {total_cert_count}</div>", unsafe_allow_html=True)
        # Add counts to labels
        labels_cert_with_counts = [f"{label} ({count})" for label, count in zip(labels_cert, values_cert)]
        fig_cert = go.Figure(data=[go.Pie(labels=labels_cert_with_counts, values=values_cert, marker=dict(colors=colors_cert))])
        fig_cert.update_layout(height=450, width=450)
        st.plotly_chart(fig_cert, use_container_width=True, key="cert_chart")

    with col2:
        total_soc_count = int(count_by_criticité_soc['count'].sum())  # Convert to integer
        st.markdown(f"<div class='box'><div class='header'>Incidents SOC: {total_soc_count}</div>", unsafe_allow_html=True)
        # Add counts to labels
        labels_soc_with_counts = [
        f"{criticité} ({int(count)})" for criticité, count in zip(count_by_criticité_soc['criticité'], count_by_criticité_soc['count'])
    ]

        fig_soc = go.Figure(data=[go.Pie(labels=labels_soc_with_counts, values=count_by_criticité_soc['count'], marker=dict(colors=colors_cert))])
        fig_soc.update_layout(height=450, width=450)
        st.plotly_chart(fig_soc, use_container_width=True, key="soc_chart")

    with col3[0]:
    ## Contre Analyses Section
        with st.container():
            # Dynamically define the monthly count based on the selected year
            if selected_year == 2025:
                monthly_counts = {
                    "Janvier": 45,
                    "Février": 18,
                    "Mars": 18,
                    "Avril": 8,
                    "Mai": "N/A",
                    "Juin": "N/A",
                    "Juillet": "N/A",
                    "Août": "N/A",
                    "Septembre": "N/A",
                    "Octobre": "N/A",
                    "Novembre": "N/A",
                    "Décembre": "N/A"
                }
            else:
                monthly_counts = {
                    "Janvier": 12,
                    "Février": 16,
                    "Mars": 16,
                    "Avril": 24,
                    "Mai": 44,
                    "Juin": 17,
                    "Juillet": 17,
                    "Août": 20,
                    "Septembre": 26,
                    "Octobre": 20,
                    "Novembre": 29,
                    "Décembre": 36,
                }

            # Compute count based on selection
            if selected_month == "Tous les mois":
                # Sum only valid numeric values (ignore "N/A")
                contre_analyse_count = sum(value for value in monthly_counts.values() if isinstance(value, int))
            else:
                contre_analyse_count = monthly_counts.get(selected_month, "N/A")

            # Display the result
            st.markdown(f"<div class='box'><div class='header'>Contre Analyses: {contre_analyse_count}</div></div>", unsafe_allow_html=True)

        

        with col5:
           
            
            plans_remediation_section()

            
            # Display in Streamlit
            with col4:
                unique_scan_data = df_filtered_scan.groupby(['Month', 'Label'], as_index=False)['Actual'].first()
                total_scan_count = unique_scan_data[unique_scan_data['Label'] != 'GLOBAL']['Actual'].sum()


                st.markdown(f"<div class='box'><div class='header'>Scans Qualys: {total_scan_count}</div>", unsafe_allow_html=True)
                st.plotly_chart(fig_scan, use_container_width=True, key="scan_chart")
                st.markdown("</div>", unsafe_allow_html=True)


if __name__ == "__main__":
    app()
