import streamlit as st
import pandas as pd
import time
import plotly.graph_objects as go
from database import get_connection, insert_row
import io

if __name__ == "__main__":
    st.set_page_config(
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded"
    )

st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #2b6cb0;
        margin-bottom: 1rem;
    }
    .filter-section {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .data-stats {
        font-size: 1.1rem;
        font-weight: bold;
        color: #4a5568;
    }
    .stDataFrame {
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
</style>
""", unsafe_allow_html=True)

def load_data():
    with st.spinner("Connexion à la base de données..."):
        try:
            conn = get_connection()
            query = 'SELECT * FROM "MASTER_TABLE"'
            df = pd.read_sql(query, conn)
            conn.close()
            return df
        except Exception as e:
            st.error(f"Erreur base de données : {e}")
            return None

def categorize_environment(file_name):
    file_name_lower = file_name.lower()
    if "hors" in file_name_lower:
        return "HORS PROD"
    elif "prod" in file_name_lower:
        return "PROD"
    return "HORS PROD"

def detect_produit(file_name):
    file_name_lower = file_name.lower()
    if "hra" in file_name_lower:
        return "HRa"
    elif any(term in file_name_lower for term in ["ple", "pleiades"]):
        return "Pleiades"
    elif "gp4you" in file_name_lower:
        return "GP4you"
    elif "4you" in file_name_lower:
        return "4You"
    return "Jira"

def plot_stacked_bar_chart(df):
    p1_counts = df[df['Priorité'] == 'P1']['Vérification'].value_counts()
    p2_counts = df[df['Priorité'] == 'P2']['Vérification'].value_counts()
    p3_counts = df[df['Priorité'] == 'P3']['Vérification'].value_counts()
    categories = ['Pas confirmée', 'Confirmée']
    fig = go.Figure()
    fig.add_trace(go.Bar(name='P1', x=categories, y=[p1_counts.get(cat, 0) for cat in categories],
                         marker_color='#FF0000', text=[p1_counts.get(cat, 0) for cat in categories],
                         textposition='inside', textfont=dict(color='white', size=14)))
    fig.add_trace(go.Bar(name='P2', x=categories, y=[p2_counts.get(cat, 0) for cat in categories],
                         marker_color='#ED7D31', text=[p2_counts.get(cat, 0) for cat in categories],
                         textposition='inside', textfont=dict(color='white', size=14)))
    fig.add_trace(go.Bar(name='P3', x=categories, y=[p3_counts.get(cat, 0) for cat in categories],
                         marker_color='#FFC000', text=[p3_counts.get(cat, 0) for cat in categories],
                         textposition='inside', textfont=dict(color='black', size=14)))
    fig.update_layout(barmode='stack', title="Nombre de failles confirmées et non confirmées par criticité",
                      xaxis_title="Statut", yaxis_title="Nombre de failles")
    return fig

def plot_contranalyses_chart(df):
    count_per_month = df.groupby("Mois")["Nom fichier"].nunique().reset_index()
    months_order = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
    count_per_month["Mois"] = pd.Categorical(count_per_month["Mois"], categories=months_order, ordered=True)
    count_per_month = count_per_month.sort_values("Mois")
    fig = go.Figure()
    fig.add_trace(go.Bar(
        x=count_per_month["Mois"],
        y=count_per_month["Nom fichier"],
        text=count_per_month["Nom fichier"],
        textposition='auto',
        marker_color='#7aa9ce'
    ))
    fig.update_layout(
        title="Nombre de contre analyses par mois",
        xaxis_title="Mois",
        yaxis_title="Nombre de contre analyses"
    )
    return fig

def apply_priority_colors(df):
    def priority_color(row):
        if row['Priorité'] == 'P1':
            return ['background-color: #FF0000; color: white'] * len(row)
        elif row['Priorité'] == 'P2':
            return ['background-color: #ED7D31; color: black'] * len(row)
        elif row['Priorité'] == 'P3':
            return ['background-color: #FFC000; color: black'] * len(row)
        else:
            return [''] * len(row)
    return df.style.apply(priority_color, axis=1)

def main():
    df = load_data()
    if df is None:
        return

    df["Environnement"] = df["Nom fichier"].apply(categorize_environment)
    df["Produit"] = df["Nom fichier"].apply(detect_produit)
    df["Vérification"] = df["Vérification"].astype(str).str.strip().str.lower().apply(
        lambda x: "Confirmée" if "confirmée" in x and "pas" not in x else (
            "Pas confirmée" if "pas" in x else pd.NA)
    )
    df = df[~(df["Vulnérabilité"].isnull() & df["Priorité"].isnull())]

    filtered_df = df.copy()

    col1, col2 = st.columns(2)
    year_options = ["2025"]
    selected_year = col1.selectbox("Année :", year_options, index=0)
    temp_df = filtered_df[filtered_df["Année"] == selected_year]



    months_order = ["Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"]
    unique_months = temp_df["Mois"].dropna().unique().tolist()
    sorted_months = [m for m in months_order if m in unique_months]
    month_options = ["Tous les mois"] + sorted_months
    selected_month = col2.selectbox("Mois :", month_options)
    temp_df = temp_df if selected_month == "Tous les mois" else temp_df[temp_df["Mois"] == selected_month]

    col3, col4 = st.columns(2)
    client_options = ["Tous les clients"] + sorted(temp_df["Nom Client"].dropna().unique().tolist())
    selected_client = col3.selectbox("Client :", client_options)
    temp_df = temp_df if selected_client == "Tous les clients" else temp_df[temp_df["Nom Client"] == selected_client]

    statut_options = ["Tous les statuts"] + sorted(temp_df["STATUT"].dropna().unique().tolist())
    selected_statut = col4.selectbox("Statut :", statut_options)
    temp_df = temp_df if selected_statut == "Tous les statuts" else temp_df[temp_df["STATUT"] == selected_statut]


    col5, col6 = st.columns(2)
    env_options = ["Tous"] + sorted(temp_df["Environnement"].dropna().unique().tolist())
    selected_env = col5.selectbox("Environnement :", env_options)
    temp_df = temp_df if selected_env == "Tous" else temp_df[temp_df["Environnement"] == selected_env]

    prod_options = ["Tous"] + sorted(temp_df["Produit"].dropna().unique().tolist())
    selected_prod = col6.selectbox("Produit :", prod_options)
    final_df = temp_df if selected_prod == "Tous" else temp_df[temp_df["Produit"] == selected_prod]

    st.markdown('</div>', unsafe_allow_html=True)

    table_df = final_df[final_df["Vérification"] == "Confirmée"]
 
    display_cols = ["Nom Client", "Vulnérabilité", "Priorité", "Environnement", "Produit", "Mois","Ticket JIRA", "STATUT"]
    display_df = table_df[display_cols].copy()
    display_df = display_df.applymap(lambda x: str(x).replace('\n', ' ') if isinstance(x, str) else x)

    def style_jira_link(row):
        url = row["Ticket JIRA"]
        if pd.notna(url) and url.startswith("http"):
            color = "white" if row["Priorité"] == "P1" else "black"
            return f'<a href="{url}" target="_blank" style="color: {color}; text-decoration: none;">{url}</a>'
        return url

    display_df["Ticket JIRA"] = display_df.apply(style_jira_link, axis=1)



    export_df = table_df.copy()

    if len(final_df) > 0:
        col_chart1, col_chart2 = st.columns(2)
        with col_chart1:
            contranalyse_fig = plot_contranalyses_chart(final_df)
            st.plotly_chart(contranalyse_fig, use_container_width=True)
        with col_chart2:
            fig = plot_stacked_bar_chart(final_df)
            st.plotly_chart(fig, use_container_width=True)

    if len(table_df) > 0:
        _, col_toggle = st.columns([8, 1])
        show_table = col_toggle.toggle("Afficher", value=True)

        if show_table:
            display_df['Priorité'] = pd.Categorical(display_df['Priorité'], categories=['P1', 'P2', 'P3'], ordered=True)
            display_df = display_df.sort_values('Priorité')
            st.markdown("### Tableau des vulnérabilités confirmées")
            styled_table = apply_priority_colors(display_df)
            styled_table = styled_table.hide(axis="index")
            st.write(styled_table.to_html(index=False), unsafe_allow_html=True)

           
            output = io.BytesIO()

            # Sort and remove unwanted column
            export_df["Priorité"] = pd.Categorical(export_df["Priorité"], categories=["P1", "P2", "P3"], ordered=True)
            export_df = export_df.sort_values("Priorité")
            export_df = export_df.drop(columns=["Level"])  # Remove 'Level' from export

            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                workbook = writer.book
                worksheet = workbook.add_worksheet("Données")
                writer.sheets["Données"] = worksheet

                # Define formats
                header_format = workbook.add_format({'bold': True, 'bg_color': '#D9E1F2', 'align': 'center', 'valign': 'vcenter', 'border': 1})
                format_p1 = workbook.add_format({'bg_color': '#FF0000', 'font_color': 'white', 'align': 'center', 'valign': 'vcenter', 'border': 1})
                format_p2 = workbook.add_format({'bg_color': '#ED7D31', 'font_color': 'black', 'align': 'center', 'valign': 'vcenter', 'border': 1})
                format_p3 = workbook.add_format({'bg_color': '#FFC000', 'font_color': 'black', 'align': 'center', 'valign': 'vcenter', 'border': 1})
                format_default = workbook.add_format({'align': 'center', 'valign': 'vcenter', 'border': 1})

                # Write header
                for col_idx, col in enumerate(export_df.columns):
                    worksheet.write(0, col_idx, col, header_format)
                    if col in ["Nom Client", "Année", "Vérification", "Priorité", "Mois", "STATUT", "Produit", "Fix Type", "Environnement"]:
                        worksheet.set_column(col_idx, col_idx, 12)
                    else:
                        worksheet.set_column(col_idx, col_idx, 35)

                # Write data
                for row_idx, (_, row) in enumerate(export_df.iterrows(), start=1):
                    priority = row.get("Priorité", "")
                    fmt = format_default
                    if priority == "P1":
                        fmt = format_p1
                    elif priority == "P2":
                        fmt = format_p2
                    elif priority == "P3":
                        fmt = format_p3

                    for col_idx, value in enumerate(row):
                        worksheet.write(row_idx, col_idx, value, fmt)

                writer.close()

            excel_data = output.getvalue()


            st.download_button(
                label="📅 Exporter en Excel",
                data=excel_data,
                file_name=f"Contre Analyses 2025.xlsx",
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
    else:
        st.warning("Aucune donnée 'Confirmée' à afficher dans le tableau.")

    user_info = st.session_state.get('keycloak_user_info', {})
    username = user_info.get('preferred_username', '')
    email = user_info.get('email', '')

    if username == 'nour.bachraoui' or email == '<EMAIL>':
        st.markdown("---")
        st.subheader("Ajouter une nouvelle ligne")

        with st.form("add_row_form"):
            nom_client = st.text_input("Nom Client").upper()
            level = st.selectbox("Level", ["Level1", "Level2", "Level3", "Level4", "Level5"])
            vulnerabilite = st.text_input("Vulnérabilité")
            verification = st.selectbox("Vérification", ["Confirmée", "Pas confirmée"])
            description = st.text_input("Description")
            impact = st.text_input("Impact")
            priorite = st.selectbox("Priorité", ["P1", "P2", "P3"])
            recommandation = st.text_input("Recommandation")
            fix_type = st.selectbox("Fix Type", ["Infrastructure", "Applicatif"])
            ticket_jira = st.text_input("Ticket JIRA")
            environnement = st.selectbox("Environnement", ["PROD", "HORS PROD"])
            produit = st.selectbox("Produit", ["4You", "GP4you", "HRa", "Pleiades", "Jira"])
            mois = st.selectbox("Mois", months_order)
            annee = st.selectbox("Année", ["2025"])

            submitted = st.form_submit_button("Ajouter")

            if submitted:
                nom_fichier = f"{nom_client}-Pôle_Sécurité-Contre_Analyse_Qualys-{produit}-Black_Box-{environnement}-V1.0"
                row_data = {
                    "Level": level,
                    "Vulnérabilité": vulnerabilite,
                    "Vérification": verification,
                    "Description": description,
                    "Impact": impact,
                    "Priorité": priorite,
                    "Recommandation": recommandation,
                    "Fix Type": fix_type,
                    "Ticket JIRA": ticket_jira,
                    "Nom Client": nom_client,
                    "Nom fichier": nom_fichier,
                    "Année": annee,
                    "Mois": mois
                }

                try:
                    conn = get_connection()
                    success, error_msg = insert_row(conn, "MASTER_TABLE", row_data)
                    conn.close()

                    if success:
                        st.success("\u2705 Nouvelle ligne ajoutée avec succès !")
                    else:
                        st.error(f"\u274c Erreur lors de l'ajout : {error_msg}")
                except Exception as e:
                    st.error(f"\u274c Exception inattendue : {e}")

if __name__ == "__main__":
    main()
